hoistPattern:
  - '*'
hoistedDependencies:
  '@alcalzone/ansi-tokenize@0.1.3':
    '@alcalzone/ansi-tokenize': private
  '@colors/colors@1.5.0':
    '@colors/colors': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@esbuild/aix-ppc64@0.25.4':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.25.4':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.25.4':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.25.4':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.25.4':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.25.4':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.25.4':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.25.4':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.25.4':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.25.4':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.25.4':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.25.4':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.25.4':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.25.4':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.25.4':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.25.4':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.25.4':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-arm64@0.25.4':
    '@esbuild/netbsd-arm64': private
  '@esbuild/netbsd-x64@0.25.4':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-arm64@0.25.4':
    '@esbuild/openbsd-arm64': private
  '@esbuild/openbsd-x64@0.25.4':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.25.4':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.25.4':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.25.4':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.25.4':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@rollup/rollup-android-arm-eabi@4.41.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.41.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.41.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.41.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.41.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.41.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.41.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.41.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.41.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.41.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.41.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.41.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.41.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.41.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.41.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.41.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.41.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.41.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.41.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.41.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@sindresorhus/is@4.6.0':
    '@sindresorhus/is': private
  '@tokenizer/inflate@0.2.7':
    '@tokenizer/inflate': private
  '@tokenizer/token@0.3.0':
    '@tokenizer/token': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/body-parser@1.19.5':
    '@types/body-parser': private
  '@types/cardinal@2.1.1':
    '@types/cardinal': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.4':
    '@types/http-errors': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/node@22.15.21':
    '@types/node': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.4':
    '@types/send': private
  '@types/serve-static@1.15.7':
    '@types/serve-static': private
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@7.18.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@7.18.0(eslint@8.57.1)(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vitest/expect@3.1.4':
    '@vitest/expect': private
  '@vitest/mocker@3.1.4(vite@6.3.5(@types/node@22.15.21))':
    '@vitest/mocker': private
  '@vitest/pretty-format@3.1.4':
    '@vitest/pretty-format': private
  '@vitest/runner@3.1.4':
    '@vitest/runner': private
  '@vitest/snapshot@3.1.4':
    '@vitest/snapshot': private
  '@vitest/spy@3.1.4':
    '@vitest/spy': private
  '@vitest/utils@3.1.4':
    '@vitest/utils': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@2.0.0:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agent-base@7.1.3:
    agent-base: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv@6.12.6:
    ajv: private
  ansi-align@3.0.1:
    ansi-align: private
  ansi-escapes@7.0.0:
    ansi-escapes: private
  ansi-regex@6.1.0:
    ansi-regex: private
  ansi-styles@6.2.1:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  arg@4.1.3:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  assertion-error@2.0.1:
    assertion-error: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  auto-bind@5.0.1:
    auto-bind: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  balanced-match@1.0.2:
    balanced-match: private
  body-parser@2.2.0:
    body-parser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bundle-name@4.1.0:
    bundle-name: private
  bytes@3.1.2:
    bytes: private
  cac@6.7.14:
    cac: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase@8.0.0:
    camelcase: private
  chai@5.2.0:
    chai: private
  char-regex@1.0.2:
    char-regex: private
  check-error@2.1.1:
    check-error: private
  cli-boxes@3.0.0:
    cli-boxes: private
  cli-cursor@4.0.0:
    cli-cursor: private
  cli-highlight@2.1.11:
    cli-highlight: private
  cli-spinners@3.2.0:
    cli-spinners: private
  cli-table3@0.6.5:
    cli-table3: private
  cli-truncate@4.0.0:
    cli-truncate: private
  cliui@7.0.4:
    cliui: private
  code-excerpt@4.0.0:
    code-excerpt: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@1.0.0:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  convert-to-spaces@2.0.1:
    convert-to-spaces: private
  cookie-signature@1.2.2:
    cookie-signature: private
  cookie@0.7.2:
    cookie: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  deep-eql@5.0.2:
    deep-eql: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@2.1.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@10.4.0:
    emoji-regex: private
  emojilib@2.4.0:
    emojilib: private
  encodeurl@2.0.0:
    encodeurl: private
  environment@1.1.0:
    environment: private
  es-abstract@1.23.10:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es-toolkit@1.38.0:
    es-toolkit: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@7.18.0(eslint@8.57.1)(typescript@5.8.3))(eslint-import-resolver-node@0.3.9)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  eslint@8.57.1:
    eslint: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@3.0.3:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  expect-type@1.2.1:
    expect-type: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.4(picomatch@4.0.2):
    fdir: private
  fflate@0.8.2:
    fflate: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@2.1.0:
    finalhandler: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.2:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@2.0.0:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-east-asian-width@1.3.0:
    get-east-asian-width: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  highlight.js@10.7.3:
    highlight.js: private
  http-errors@2.0.0:
    http-errors: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  indent-string@5.0.0:
    indent-string: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@4.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-in-ci@1.0.0:
    is-in-ci: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-promise@4.0.0:
    is-promise: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-wsl@3.1.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@3.1.1:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  js-tokens@4.0.0:
    js-tokens: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  loupe@3.1.3:
    loupe: private
  magic-string@0.30.17:
    magic-string: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@1.1.0:
    media-typer: private
  merge-descriptors@2.0.0:
    merge-descriptors: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.54.0:
    mime-db: private
  mime-types@3.0.1:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  ms@2.1.3:
    ms: private
  mz@2.7.0:
    mz: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@1.0.0:
    negotiator: private
  node-domexception@1.0.0:
    node-domexception: private
  node-emoji@2.2.0:
    node-emoji: private
  node-fetch@2.7.0:
    node-fetch: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  optionator@0.9.4:
    optionator: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  parse5-htmlparser2-tree-adapter@6.0.1:
    parse5-htmlparser2-tree-adapter: private
  parse5@5.1.1:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  patch-console@2.0.0:
    patch-console: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-to-regexp@8.2.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@2.0.3:
    pathe: private
  pathval@2.0.0:
    pathval: private
  peek-readable@7.0.0:
    peek-readable: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.2:
    picomatch: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.3:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  proxy-addr@2.0.7:
    proxy-addr: private
  qs@6.14.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-is@16.13.1:
    react-is: private
  react-reconciler@0.29.2(react@18.3.1):
    react-reconciler: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  require-directory@2.1.1:
    require-directory: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@2.0.0-next.5:
    resolve: private
  restore-cursor@4.0.0:
    restore-cursor: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.41.1:
    rollup: private
  router@2.2.0:
    router: private
  run-applescript@7.0.0:
    run-applescript: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  send@1.2.0:
    send: private
  serve-static@2.2.0:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  siginfo@2.0.0:
    siginfo: private
  signal-exit@3.0.7:
    signal-exit: private
  skin-tone@2.0.0:
    skin-tone: private
  slash@3.0.0:
    slash: private
  slice-ansi@7.1.0:
    slice-ansi: private
  source-map-js@1.2.1:
    source-map-js: private
  stack-utils@2.0.6:
    stack-utils: private
  stackback@0.0.2:
    stackback: private
  statuses@2.0.1:
    statuses: private
  std-env@3.9.0:
    std-env: private
  string-width@7.2.0:
    string-width: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strtok3@10.2.2:
    strtok3: private
  supports-color@7.2.0:
    supports-color: private
  supports-hyperlinks@3.2.0:
    supports-hyperlinks: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  tinybench@2.9.0:
    tinybench: private
  tinyexec@0.3.2:
    tinyexec: private
  tinyglobby@0.2.13:
    tinyglobby: private
  tinypool@1.0.2:
    tinypool: private
  tinyrainbow@2.0.0:
    tinyrainbow: private
  tinyspy@3.0.2:
    tinyspy: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  token-types@6.0.0:
    token-types: private
  tr46@5.1.1:
    tr46: private
  ts-api-utils@1.4.3(typescript@5.8.3):
    ts-api-utils: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uint8array-extras@1.4.0:
    uint8array-extras: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@5.26.5:
    undici-types: private
  unicode-emoji-modifier-base@1.0.0:
    unicode-emoji-modifier-base: private
  unpipe@1.0.0:
    unpipe: private
  uri-js@4.4.1:
    uri-js: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  vite-node@3.1.4(@types/node@22.15.21):
    vite-node: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@7.0.0:
    webidl-conversions: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  why-is-node-running@2.3.0:
    why-is-node-running: private
  widest-line@5.0.0:
    widest-line: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@9.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.2:
    ws: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@20.2.9:
    yargs-parser: private
  yargs@16.2.0:
    yargs: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoga-layout@3.2.1:
    yoga-layout: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Sat, 24 May 2025 17:33:16 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.4'
  - '@esbuild/android-arm64@0.25.4'
  - '@esbuild/android-arm@0.25.4'
  - '@esbuild/android-x64@0.25.4'
  - '@esbuild/darwin-arm64@0.25.4'
  - '@esbuild/darwin-x64@0.25.4'
  - '@esbuild/freebsd-arm64@0.25.4'
  - '@esbuild/freebsd-x64@0.25.4'
  - '@esbuild/linux-arm64@0.25.4'
  - '@esbuild/linux-arm@0.25.4'
  - '@esbuild/linux-ia32@0.25.4'
  - '@esbuild/linux-loong64@0.25.4'
  - '@esbuild/linux-mips64el@0.25.4'
  - '@esbuild/linux-ppc64@0.25.4'
  - '@esbuild/linux-riscv64@0.25.4'
  - '@esbuild/linux-s390x@0.25.4'
  - '@esbuild/netbsd-arm64@0.25.4'
  - '@esbuild/netbsd-x64@0.25.4'
  - '@esbuild/openbsd-arm64@0.25.4'
  - '@esbuild/openbsd-x64@0.25.4'
  - '@esbuild/sunos-x64@0.25.4'
  - '@esbuild/win32-arm64@0.25.4'
  - '@esbuild/win32-ia32@0.25.4'
  - '@esbuild/win32-x64@0.25.4'
  - '@rollup/rollup-android-arm-eabi@4.41.1'
  - '@rollup/rollup-android-arm64@4.41.1'
  - '@rollup/rollup-darwin-arm64@4.41.1'
  - '@rollup/rollup-darwin-x64@4.41.1'
  - '@rollup/rollup-freebsd-arm64@4.41.1'
  - '@rollup/rollup-freebsd-x64@4.41.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.41.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.41.1'
  - '@rollup/rollup-linux-arm64-gnu@4.41.1'
  - '@rollup/rollup-linux-arm64-musl@4.41.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.41.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.41.1'
  - '@rollup/rollup-linux-riscv64-musl@4.41.1'
  - '@rollup/rollup-linux-s390x-gnu@4.41.1'
  - '@rollup/rollup-win32-arm64-msvc@4.41.1'
  - '@rollup/rollup-win32-ia32-msvc@4.41.1'
  - '@rollup/rollup-win32-x64-msvc@4.41.1'
  - fsevents@2.3.3
storeDir: /mnt/c/.pnpm-store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
