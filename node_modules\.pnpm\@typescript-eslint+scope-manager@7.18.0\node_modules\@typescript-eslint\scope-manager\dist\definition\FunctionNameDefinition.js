"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FunctionNameDefinition = void 0;
const DefinitionBase_1 = require("./DefinitionBase");
const DefinitionType_1 = require("./DefinitionType");
class FunctionNameDefinition extends DefinitionBase_1.DefinitionBase {
    constructor(name, node) {
        super(DefinitionType_1.DefinitionType.FunctionName, name, node, null);
        this.isTypeDefinition = false;
        this.isVariableDefinition = true;
    }
}
exports.FunctionNameDefinition = FunctionNameDefinition;
//# sourceMappingURL=FunctionNameDefinition.js.map