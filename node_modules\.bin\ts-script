#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules/ts-node/dist/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules/ts-node/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/ts-node@10.9.2_@types+node@22.15.21_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../ts-node/dist/bin-script-deprecated.js" "$@"
else
  exec node  "$basedir/../ts-node/dist/bin-script-deprecated.js" "$@"
fi
